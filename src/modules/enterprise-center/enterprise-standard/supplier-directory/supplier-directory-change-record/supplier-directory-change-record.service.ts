import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import {
  ChangeRecordDetailDto,
  DetailsFieldType
} from './supplier-directory-change-record.dto';

@Injectable()
export class SupplierDirectoryChangeRecordService {
  constructor(private readonly prisma: PrismaService) {}

  async add(
    prisma: PrismaService,
    changeList: ChangeRecordDetailDto[],
    reqUser: IReqUser,
    id: string,
    changeNum: number
  ) {
    let data: any;
    if (changeList.length) {
      data = await prisma.supplierDirectoryChangeRecord.create({
        data: {
          changeNum,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          createBy: reqUser.id,
          updateBy: reqUser.id,
          supplierDirectoryId: id,
          changeBy: reqUser.nickname
        }
      });
      await prisma.supplierDirectoryChangeRecordDetails.createMany({
        data: changeList.map((item) => ({
          ...item,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          supplierDirectoryId: id,
          supplierDirectoryChangeRecordId: data.id,
          createBy: reqUser.id,
          updateBy: reqUser.id
        }))
      });
    }
    return data;
  }

  // async getRecords(supplierDirectoryId: string) {
  //   return await this.prisma.supplierDirectoryChangeRecord.findMany({
  //     select: {
  //       id: true,
  //       supplierDirectoryId: true,
  //       changeBy: true,
  //       changeNum: true,
  //       createAt: true
  //     },
  //     where: {
  //       supplierDirectoryId,
  //       isDeleted: false
  //     },
  //     orderBy: {
  //       createAt: 'desc'
  //     }
  //   });
  // }

  async getDetails(id: string) {
    const data = await this.prisma.supplierDirectoryChangeRecord.findMany({
      select: {
        id: true,
        changeBy: true,
        createAt: true,
        changeNum: true,
        details: {
          select: {
            id: true,
            fieldName: true,
            oldValue: true,
            newValue: true,
            changeType: true,
            fieldType: true
          },
          orderBy: {
            fieldType: 'asc'
          }
        }
      },
      where: {
        // id,
        supplierDirectoryId: id,
        isDeleted: false
      },
      orderBy: {
        createAt: 'desc'
      }
    });
    return data.map((item) => {
      return {
        ...item,
        changeNum:
          item.details.filter(
            (detail) => detail.fieldType === DetailsFieldType.GENERAL
          ).length +
          (item.details.filter(
            (detail) => detail.fieldType === DetailsFieldType.FILE
          ).length
            ? 1
            : 0)
      };
    });
  }
}
