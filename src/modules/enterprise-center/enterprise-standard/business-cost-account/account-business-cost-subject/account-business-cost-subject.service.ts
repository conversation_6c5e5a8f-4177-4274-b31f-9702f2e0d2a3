import { Injectable } from '@nestjs/common';
import { isEmpty } from 'lodash';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { PlatformService } from '@/modules/platform/platform.service';
import { EnableStatus, Prisma } from '@/prisma/generated';

import {
  CreateAccountDto,
  DictionaryCategoryType,
  DictionaryDetailType,
  DictionaryType
} from '../business-cost-account.dto';
import { BusinessCostAccountService } from '../business-cost-account.service';

@Injectable()
export class AccountBusinessCostSubjectService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly service: BusinessCostAccountService,
    private readonly platformService: PlatformService
  ) {}

  async getVersionList(req: Request, reqUser: IReqUser, data: DictionaryType) {
    const { accountDictionaryId } = data;
    // 查询当前公司的最上级组织
    const topOrg = await this.platformService.getOrgParentIds(
      req,
      reqUser.tenantId,
      reqUser.orgId
    );
    let result = await this.prisma.$queryRaw<any[]>`
      SELECT 
        bcsv.id,
        bcsv.tenant_id,
        bcsv.org_id,
        bcsv.name,
        o.name AS org_name,
        bcsv.status,
        EXISTS (
          SELECT 1 
          FROM account_business_cost_subject_version adv
          WHERE adv.tenant_id = bcsv.tenant_id
            AND adv.org_id = ${reqUser.orgId}
            AND adv.version_id = bcsv.id
            AND adv.account_dictionary_id = ${accountDictionaryId}
            AND adv.is_deleted = false
        ) AS is_use
      FROM business_cost_subject_version bcsv
      JOIN platform_meta.org AS o
        ON o.tenant_id = bcsv.tenant_id
        AND o.id = bcsv.org_id
        AND o.is_deleted = false
      WHERE bcsv.is_deleted = false 
        AND bcsv.status != 'NOT_ENABLED' 
        AND bcsv.tenant_id = ${reqUser.tenantId}
        AND bcsv.org_id = ANY(${topOrg}::text[])
      ORDER BY bcsv.create_by DESC
    `;
    // 不是已启用并且没有被项目层选中的版本不展示
    result = result.filter(
      (item) => item.isUse || item.status === EnableStatus.ENABLED
    );
    return result;
  }

  async getCategoryList(reqUser: IReqUser, data: DictionaryCategoryType) {
    const { versionId } = data;
    return await this.prisma.$queryRaw<any[]>`
         SELECT
          bcsv.id,
          bcsv.tenant_id,
          bcsv.org_id,
          bcsv.name,
          bcsv.code,
          bcsv.parent_id,
          bcsv.remark,
          bcsv.business_cost_subject_version_id as version_id
        FROM business_cost_subject_category bcsv
        WHERE bcsv.is_deleted = false
          AND bcsv.tenant_id = ${reqUser.tenantId}
          AND bcsv.business_cost_subject_version_id = ${versionId}
          AND bcsv.is_deleted = false
        ORDER BY bcsv."level", bcsv.sort
        `;
  }

  async getDetailList(reqUser: IReqUser, data: DictionaryDetailType) {
    const { versionId, categoryId } = data;

    const categories = await this.prisma.$queryRaw<any[]>`
      select
        id
      from business_cost_subject_category
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and position(${categoryId} in full_id) > 0
        and is_leaf = true
    `;
    const categoryIds = categories.map((item) => item.id);
    if (isEmpty(categoryIds) && versionId !== categoryId) return [];

    const res = await this.prisma.$queryRaw<any[]>`
      SELECT
        bcsv.id,
        bcsv.tenant_id,
        bcsv.org_id,
        bcsv.name,
        bcsv.business_cost_subject_version_id as version_id,
        bcsv.business_cost_subject_category_id as category_id,
        bcsv.financial_cost_subject_id,
        bcsv.code,
        bcsv.unit,
        bcsv.expense_category,
        bcsv.accounting_description,
        bcsv.is_safety_construction_fee,
        bcsv.subject_mapping_description,
        fcs.name as financial_cost_subject_name
      FROM business_cost_subject_detail bcsv
      left join financial_cost_subject fcs
        on fcs.is_deleted = false
        and fcs.tenant_id = bcsv.tenant_id
        and fcs.id = bcsv.financial_cost_subject_id
      WHERE bcsv.is_deleted = false
        AND bcsv.tenant_id = ${reqUser.tenantId}
        ${
          categoryId === versionId
            ? Prisma.empty
            : Prisma.sql`and bcsv.business_cost_subject_category_id in (${Prisma.join(categoryIds)})`
        }
        AND bcsv.is_active = true
      ORDER BY bcsv.business_cost_subject_category_id, bcsv.sort
    `;
    return res;
  }

  async add(reqUser: IReqUser, data: CreateAccountDto) {
    await this.prisma.$transaction(async (tx) => {
      // 先删除旧的版本
      await tx.accountBusinessCostSubjectVersion.updateMany({
        where: {
          accountDictionaryId: data.accountDictionaryId,
          isDeleted: false,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
      // 添加新的版本
      await tx.accountBusinessCostSubjectVersion.create({
        data: {
          ...data,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          createBy: reqUser.id,
          updateBy: reqUser.id
        }
      });
      // 修改配置状态为已配置
      await this.service.updateStatus(
        tx as PrismaService,
        reqUser,
        data.accountDictionaryId,
        true
      );
    });
    return true;
  }

  async getChooseVersionList(reqUser: IReqUser, data: DictionaryType) {
    const result = await this.prisma.$queryRaw`
      with temp_version as (
        select 
          bcsv.id
          , bcsv.name
          , null as parent_id
          , null as code
          , null as remark
          , bcsv.id as version_id
        from account_business_cost_subject_version abcsv
        join business_cost_subject_version bcsv 
          on bcsv.is_deleted = false
          and bcsv.tenant_id = abcsv.tenant_id
          and bcsv.id = abcsv.version_id
          and bcsv.status != 'NOT_ENABLED'
        where abcsv.is_deleted = false 
          and abcsv.tenant_id = ${reqUser.tenantId}
          and abcsv.org_id = ${reqUser.orgId}
          and abcsv.account_dictionary_id = ${data.accountDictionaryId}
      )
      ,temp_category as (
        select
          bcsc.id
          ,bcsc.name
          ,case when bcsc.parent_id is null then bcsc.business_cost_subject_version_id else bcsc.parent_id end as parent_id
          ,bcsc.code
          ,bcsc.remark
          ,bcsc.business_cost_subject_version_id as version_id
        from business_cost_subject_category bcsc
        join temp_version
          on temp_version.id = bcsc.business_cost_subject_version_id
        where bcsc.is_deleted = false
          and bcsc.tenant_id = ${reqUser.tenantId}
        order by bcsc.level, bcsc.sort
      )
      select * from temp_version
      union all
      select * from temp_category
    `;
    return result;
  }

  async delete(versionId: string, reqUser: IReqUser, data: DictionaryType) {
    // 缺少删除的引用判断，后续补上
    await this.prisma.$transaction(async (tx) => {
      await tx.accountBusinessCostSubjectVersion.updateMany({
        where: {
          versionId,
          isDeleted: false,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          accountDictionaryId: data.accountDictionaryId
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
      // 修改配置状态为未配置
      await this.service.updateStatus(
        tx as PrismaService,
        reqUser,
        data.accountDictionaryId,
        false
      );
    });
    return true;
  }
}
