import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable
} from '@nestjs/common';
import { isEmpty as _isEmpty } from 'lodash';

import { MoveToEnum } from '@/common/enums/common.enum';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { EnableStatus } from '@/prisma/generated';
import { BusinessCostSubjectDetailWhereInput } from '@/prisma/generated/models';
import { Utils } from '@/public/utils';

import { BusinessCostSubjectVersionService } from '../version/business-cost-subject-version.service';
import {
  BusinessCostSubjectDetailCreateDto,
  BusinessCostSubjectDetailQueryListDto,
  BusinessCostSubjectDetailUpdateDto
} from './business-cost-subject-detail.dto';

@Injectable()
export class BusinessCostSubjectDetailService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly businessCostSubjectVersionService: BusinessCostSubjectVersionService
  ) {}

  async getList(
    reqUser: IReqUser,
    query: BusinessCostSubjectDetailQueryListDto
  ) {
    const { tenantId, orgId } = reqUser;
    const {
      businessCostSubjectVersionId,
      businessCostSubjectCategoryId,
      name
    } = query;

    const where: BusinessCostSubjectDetailWhereInput = {
      tenantId,
      orgId,
      businessCostSubjectVersionId,
      isDeleted: false
    };
    // 查询所有明细时，不按分类进行过滤
    if (businessCostSubjectCategoryId !== '100') {
      where.businessCostSubjectCategoryId = businessCostSubjectCategoryId;
    }
    // 按名称搜索时
    if (name) {
      where.name = { contains: name };
    }

    const list = await this.findList(where);

    return list;
  }

  private async findList(where: BusinessCostSubjectDetailWhereInput) {
    const list = await this.prisma.businessCostSubjectDetail.findMany({
      select: {
        id: true,
        businessCostSubjectVersionId: true,
        businessCostSubjectCategoryId: true,
        financialCostSubjectId: true,
        code: true,
        name: true,
        unit: true,
        expenseCategory: true,
        accountingDescription: true,
        isSafetyConstructionFee: true,
        subjectMappingDescription: true,
        financialCostSubjectName: true,
        isActive: true,
        sort: true
      },
      where,
      orderBy: [
        { businessCostSubjectCategory: { isActive: 'desc' } },
        { businessCostSubjectCategory: { level: 'asc' } },
        { businessCostSubjectCategory: { sort: 'asc' } },
        { isActive: 'desc' },
        { sort: 'asc' }
      ]
    });

    return list;
  }

  async getTree(reqUser: IReqUser, businessCostSubjectVersionId: string) {
    const { tenantId, orgId } = reqUser;

    const categoryList = await this.prisma.businessCostSubjectCategory.findMany(
      {
        select: {
          id: true,
          parentId: true,
          code: true,
          name: true,
          isActive: true
        },
        where: {
          tenantId,
          orgId,
          businessCostSubjectVersionId,
          // isActive: true, // 先不按照启用状态进行过滤，防止界面显示乱码
          isDeleted: false
        },
        orderBy: [{ level: 'asc' }, { sort: 'asc' }]
      }
    );
    const detailList = await this.prisma.businessCostSubjectDetail.findMany({
      select: {
        id: true,
        businessCostSubjectCategoryId: true,
        code: true,
        name: true,
        isActive: true
      },
      where: {
        tenantId,
        orgId,
        businessCostSubjectVersionId,
        // isActive: true, // 先不按照启用状态进行过滤，防止界面显示乱码
        isDeleted: false
      },
      orderBy: { sort: 'asc' }
    });

    const list = [
      ...categoryList,
      ...detailList.map((item) => ({
        id: item.id,
        parentId: item.businessCostSubjectCategoryId,
        code: item.code,
        name: item.name,
        isActive: item.isActive,
        isLeaf: true
      }))
    ];

    const tree = Utils.buildTree(list, null);

    return tree;
  }

  // 查询对应的版本
  async checkVersionStatus(id: string) {
    const version = await this.prisma.businessCostSubjectVersion.findFirst({
      select: {
        status: true
      },
      where: {
        id,
        isDeleted: false
      }
    });
    if (version?.status === EnableStatus.ENABLED) {
      throw new HttpException(
        `版本已启用，无法进行操作`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async createOne(reqUser: IReqUser, data: BusinessCostSubjectDetailCreateDto) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 校验启用状态
    await this.checkVersionStatus(data.businessCostSubjectVersionId);

    await this.checkUnique({ tenantId, orgId, data });

    const maxSort = await this.prisma.businessCostSubjectDetail.aggregate({
      _max: {
        sort: true
      },
      where: {
        tenantId,
        orgId,
        businessCostSubjectVersionId: data.businessCostSubjectVersionId,
        businessCostSubjectCategoryId: data.businessCostSubjectCategoryId,
        isDeleted: false
      }
    });

    await this.prisma.$transaction(async (tx) => {
      if (
        data.isSafetyConstructionFee != null &&
        data.isSafetyConstructionFee === true
      ) {
        await tx.businessCostSubjectDetail.updateMany({
          data: {
            isSafetyConstructionFee: false
          },
          where: {
            businessCostSubjectVersionId: data.businessCostSubjectVersionId,
            businessCostSubjectCategoryId: data.businessCostSubjectCategoryId,
            isSafetyConstructionFee: true,
            isDeleted: false
          }
        });
      }
      await tx.businessCostSubjectDetail.create({
        data: {
          ...data,
          sort: (maxSort._max.sort || 0) + 1,
          tenantId,
          orgId,
          createBy: userId,
          updateBy: userId
        }
      });
    });

    return true;
  }

  async updateOne(
    reqUser: IReqUser,
    id: string,
    data: BusinessCostSubjectDetailUpdateDto
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询明细的版本
    const detail = await this.getOne(id);

    if (!detail) {
      throw new HttpException(`明细信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验版本状态
    await this.checkVersionStatus(detail.businessCostSubjectVersionId);

    await this.checkUnique({ tenantId, orgId, data, id });

    await this.prisma.$transaction(async (tx) => {
      if (
        data.isSafetyConstructionFee != null &&
        data.isSafetyConstructionFee === true
      ) {
        await tx.businessCostSubjectDetail.updateMany({
          data: {
            isSafetyConstructionFee: false
          },
          where: {
            businessCostSubjectVersionId: data.businessCostSubjectVersionId,
            isSafetyConstructionFee: true,
            isDeleted: false
          }
        });
      }
      await tx.businessCostSubjectDetail.update({
        where: {
          tenantId,
          orgId,
          id
        },
        data: {
          ...data,
          updateBy: userId
        }
      });
    });

    return true;
  }

  async moveOne(reqUser: IReqUser, id: string, moveTo: MoveToEnum) {
    const { tenantId, orgId } = reqUser;

    // 查询明细的版本
    const detail = await this.getOne(id);

    if (!detail) {
      throw new HttpException(`明细信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验版本状态
    await this.checkVersionStatus(detail.businessCostSubjectVersionId);

    const currentRecord = await this.prisma.businessCostSubjectDetail.findFirst(
      {
        select: {
          id: true,
          businessCostSubjectVersionId: true,
          businessCostSubjectCategoryId: true,
          sort: true
        },
        where: {
          tenantId,
          orgId,
          id,
          isDeleted: false
        }
      }
    );
    if (!currentRecord) {
      throw new BadRequestException('当前操作数据不存在！');
    }

    if (moveTo === MoveToEnum.UP) {
      await this.moveUp(tenantId, orgId, currentRecord);
    }
    if (moveTo === MoveToEnum.DOWN) {
      await this.moveDown(tenantId, orgId, currentRecord);
    }
  }

  private async moveUp(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      businessCostSubjectVersionId: string;
      businessCostSubjectCategoryId: string;
      sort: number;
    }
  ) {
    const { businessCostSubjectVersionId, businessCostSubjectCategoryId } =
      currentRecord;

    // 找到上一条数据
    const prevRecord = await this.prisma.businessCostSubjectDetail.findFirst({
      select: {
        id: true,
        sort: true
      },
      where: {
        tenantId,
        orgId,
        businessCostSubjectVersionId,
        businessCostSubjectCategoryId,
        isDeleted: false,
        id: {
          not: currentRecord.id
        },
        sort: {
          lt: currentRecord.sort
        }
      },
      orderBy: {
        sort: 'desc'
      }
    });

    if (!prevRecord || prevRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.businessCostSubjectDetail.update({
        where: {
          tenantId,
          orgId,
          businessCostSubjectVersionId,
          id: currentRecord.id
        },
        data: {
          sort: prevRecord.sort
        }
      }),
      this.prisma.businessCostSubjectDetail.update({
        where: {
          tenantId,
          orgId,
          businessCostSubjectVersionId,
          id: prevRecord.id
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  private async moveDown(
    tenantId: string,
    orgId: string,
    currentRecord: {
      id: string;
      businessCostSubjectVersionId: string;
      businessCostSubjectCategoryId: string;
      sort: number;
    }
  ) {
    const { businessCostSubjectVersionId, businessCostSubjectCategoryId } =
      currentRecord;

    // 找到下一条数据
    const nextRecord = await this.prisma.businessCostSubjectDetail.findFirst({
      select: {
        id: true,
        sort: true
      },
      where: {
        tenantId,
        orgId,
        businessCostSubjectVersionId,
        businessCostSubjectCategoryId,
        isDeleted: false,
        id: {
          not: currentRecord.id
        },
        sort: {
          gt: currentRecord.sort
        }
      },
      orderBy: {
        sort: 'asc'
      }
    });

    if (!nextRecord || nextRecord.sort === currentRecord.sort) {
      return;
    }

    await this.prisma.$transaction([
      this.prisma.businessCostSubjectDetail.update({
        where: {
          tenantId,
          orgId,
          businessCostSubjectVersionId,
          id: currentRecord.id
        },
        data: {
          sort: nextRecord.sort
        }
      }),
      this.prisma.businessCostSubjectDetail.update({
        where: {
          tenantId,
          orgId,
          businessCostSubjectVersionId,
          id: nextRecord.id
        },
        data: {
          sort: currentRecord.sort
        }
      })
    ]);
  }

  async getOne(id: string) {
    return this.prisma.businessCostSubjectDetail.findUnique({
      select: {
        businessCostSubjectVersionId: true
      },
      where: {
        id,
        isDeleted: false
      }
    });
  }

  async deleteOne(reqUser: IReqUser, id: string) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 查询明细的版本
    const detail = await this.getOne(id);

    if (!detail) {
      throw new HttpException(`明细信息不存在`, HttpStatus.BAD_REQUEST);
    }

    // 校验版本状态
    await this.checkVersionStatus(detail.businessCostSubjectVersionId);

    // await this.checkDeleteConstraint(tenantId, orgId, id);

    // 校验版本是否被引用
    const projectRefCount =
      await this.businessCostSubjectVersionService.getProjectRefInfo(
        tenantId,
        orgId,
        detail.businessCostSubjectVersionId
      );
    if (projectRefCount.projectRefCount > 0) {
      throw new HttpException(
        `该明细版本已被项目引用，无法删除`,
        HttpStatus.BAD_REQUEST
      );
    }

    await this.prisma.businessCostSubjectDetail.update({
      where: {
        tenantId,
        orgId,
        id
      },
      data: {
        isDeleted: true,
        updateBy: userId
      }
    });
  }

  /**
   * 检查唯一性
   */
  private async checkUnique(args: {
    tenantId: string;
    orgId: string;
    data:
      | BusinessCostSubjectDetailCreateDto
      | BusinessCostSubjectDetailUpdateDto;
    id?: string;
  }) {
    const { tenantId, orgId, data, id = '' } = args;

    const code = data.code;
    const name = data.name;

    const duplicateCode: any[] = await this.prisma.$queryRaw`
      select
        bcsd.code
        ,bcsc.name as category_name
        ,bcsc.code as category_code
      from business_cost_subject_detail bcsd
      join business_cost_subject_category bcsc
        on bcsc.is_deleted = false
        and bcsc.tenant_id = bcsd.tenant_id
        and bcsc.org_id = bcsd.org_id
        and bcsc.id = bcsd.business_cost_subject_category_id
      where bcsd.is_deleted = false
        and bcsd.tenant_id = ${tenantId}
        and bcsd.org_id = ${orgId}
        and bcsd.business_cost_subject_version_id = ${data.businessCostSubjectVersionId}
        and bcsd.code = ${code}
        and bcsd.id <> ${id}
    `;

    if (!_isEmpty(duplicateCode)) {
      const { categoryName, categoryCode, code } = duplicateCode[0];
      throw new BadRequestException(
        `业务成本科目分类[${categoryCode}-${categoryName}], 已存在编码[${code}]`
      );
    }

    const duplicateName: any[] = await this.prisma.$queryRaw`
      select
        bcsd.name
        ,bcsc.name as category_name
        ,bcsc.code as category_code
      from business_cost_subject_detail bcsd
      join business_cost_subject_category bcsc
        on bcsc.is_deleted = false
        and bcsc.tenant_id = bcsd.tenant_id
        and bcsc.org_id = bcsd.org_id
        and bcsc.id = bcsd.business_cost_subject_category_id
      where bcsd.is_deleted = false
        and bcsd.tenant_id = ${tenantId}
        and bcsd.org_id = ${orgId}
        and bcsd.business_cost_subject_version_id = ${data.businessCostSubjectVersionId}
        and bcsd.name = ${name}
        and bcsd.id <> ${id}
    `;
    if (!_isEmpty(duplicateName)) {
      const { categoryName, categoryCode, name } = duplicateName[0];
      throw new BadRequestException(
        `业务成本科目分类[${categoryCode}-${categoryName}], 已存在名称=[${name}]`
      );
    }
  }

  /**
   * 检查删除约束
   */
  async checkDeleteConstraint(tenantId: string, orgId: string, id: string) {
    // TODO: 检查删除约束
  }
}
