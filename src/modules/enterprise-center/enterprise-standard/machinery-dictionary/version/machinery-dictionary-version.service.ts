import { BadRequestException, Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { PlatformService } from '@/modules/platform/platform.service';
import { EnableStatus, Prisma } from '@/prisma/generated';

import {
  MachineryDictionaryVersionCreateDto,
  MachineryDictionaryVersionResultDto,
  MachineryDictionaryVersionUpdateDto
} from './machinery-dictionary-version.dto';

@Injectable()
export class MachineryDictionaryVersionService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly platformService: PlatformService
  ) {}

  async getList(
    reqUser: IReqUser
  ): Promise<MachineryDictionaryVersionResultDto[]> {
    const { tenantId, orgId } = reqUser;

    // 拿到当前组织的id查询该组织所有上级层级，
    // const parentOrgList =
    //   await this.platformService.getOrgParentIds(
    //     tenantId,
    //     orgId
    //   );

    // const orgIds: string[] = parentOrgList;

    const recordList = await this.prisma.machineryDictionaryVersion.findMany({
      select: {
        id: true,
        name: true,
        status: true,
        orgId: true,
        businessCostSubjectVersionId: true
      },
      where: {
        tenantId,
        orgId,
        // orgId: {
        //   in: orgIds
        // },
        isDeleted: false
      },
      orderBy: {
        createAt: 'desc'
      }
    });

    // 获取所有版本的引用数量
    const refProjectCountsList = await this.getAllProjectRefInfo(tenantId);

    const list = recordList.map((item, index) => {
      return {
        ...item,
        projectRefCount:
          refProjectCountsList.find((item1) => item1.versionId === item.id)
            ?._count._all || 0
      };
    });
    return list;
  }

  async createOne(
    reqUser: IReqUser,
    data: MachineryDictionaryVersionCreateDto
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 检查数据是否重复
    await this.checkUnique(tenantId, orgId, data);

    await this.prisma.machineryDictionaryVersion.create({
      data: {
        ...data,
        tenantId,
        orgId,
        createBy: userId,
        updateBy: userId
      }
    });
  }

  async updateOne(
    id: string,
    data: MachineryDictionaryVersionUpdateDto,
    reqUser: IReqUser
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 检查数据是否重复
    if (data.name != null) {
      await this.checkUnique(tenantId, orgId, data, id);
    }

    // 检查当前版本下的机械明细是否挂接了业务成本科目，如果挂接了，则不能切换业务成本科目版本
    await this.checkDetailIsRefBusinessCostSubject({
      tenantId,
      orgId,
      id,
      data
    });

    // 停用时，判断当前版本数据是否被引用，如果没有被引用，将状态改为“未启用”
    if (data.status === EnableStatus.NOT_ENABLED) {
      const projectRef = (await this.getProjectRefInfo(
        tenantId,
        orgId,
        id
      )) as {
        projectRefCount: number;
      };
      // 如果没有被引用，将状态改为“未启用”
      if (projectRef.projectRefCount === 0) {
        data.status = EnableStatus.NOT_ENABLED;
      }
      // 如果存在引用，将状态修改为“已停用”
      if (projectRef.projectRefCount !== 0) {
        data.status = EnableStatus.DISABLED;
      }
    }

    await this.prisma.machineryDictionaryVersion.update({
      where: {
        id,
        tenantId,
        orgId
      },
      data: {
        ...data,
        updateBy: userId
      }
    });
  }

  async deleteOne(id: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 检查删除约束
    await this.checkDeleteConstraint(tenantId, orgId, id);

    await this.prisma.machineryDictionaryVersion.update({
      where: {
        tenantId,
        orgId,
        id
      },
      data: {
        isDeleted: true,
        updateBy: userId
      }
    });
  }

  /**
   * 检查唯一性
   */
  private async checkUnique(
    tenantId: string,
    orgId: string,
    data:
      | MachineryDictionaryVersionCreateDto
      | MachineryDictionaryVersionUpdateDto,
    id?: string
  ) {
    const where: Prisma.MachineryDictionaryVersionWhereInput = {
      tenantId,
      orgId,
      name: data.name,
      isDeleted: false
    };

    // 检查名称是否重复, 排除自身
    if (id) {
      Object.assign(where, {
        id: { not: id }
      });
    }
    const duplicateRecord =
      await this.prisma.machineryDictionaryVersion.findFirst({
        select: {
          id: true
        },
        where
      });

    if (duplicateRecord) {
      throw new BadRequestException('名称重复，请重新输入！');
    }
  }

  private async checkDetailIsRefBusinessCostSubject(args: {
    tenantId: string;
    orgId: string;
    id: string;
    data: MachineryDictionaryVersionUpdateDto;
  }) {
    const { tenantId, orgId, id, data } = args;

    const currentVersion =
      await this.prisma.machineryDictionaryVersion.findFirst({
        select: {
          id: true,
          businessCostSubjectVersionId: true
        },
        where: {
          tenantId,
          orgId,
          id,
          isDeleted: false
        }
      });
    if (
      !data.businessCostSubjectVersionId ||
      (currentVersion &&
        currentVersion.businessCostSubjectVersionId ===
          data.businessCostSubjectVersionId)
    ) {
      return;
    }

    const detailList = await this.prisma.machineryDictionaryDetail.findMany({
      select: {
        id: true
      },
      where: {
        tenantId,
        orgId,
        machineryDictionaryVersionId: id,
        machineryDetailBusinessCostSubjectDetail: {
          some: {
            tenantId,
            orgId,
            isDeleted: false
          }
        },
        isDeleted: false
      }
    });

    if (detailList.length > 0) {
      throw new BadRequestException(
        '当前业务成本科目版本已被机械明细引用，请先解除引用关系'
      );
    }
  }

  /**
   * 检查删除约束
   */
  private async checkDeleteConstraint(
    tenantId: string,
    orgId: string,
    id: string
  ) {
    // 启用、停用状态的版本不能删除
    const currentRecord =
      await this.prisma.machineryDictionaryVersion.findUnique({
        select: {
          status: true
        },
        where: {
          tenantId,
          orgId,
          id,
          isDeleted: false
        }
      });
    if (currentRecord && currentRecord.status === EnableStatus.ENABLED) {
      throw new BadRequestException('启用状态的版本不可删除！');
    }
    if (currentRecord && currentRecord.status === EnableStatus.DISABLED) {
      throw new BadRequestException('停用状态的版本不可删除！');
    }

    const projectRef = (await this.getProjectRefInfo(tenantId, orgId, id)) as {
      projectRefCount: number;
    };
    if (projectRef.projectRefCount > 0) {
      throw new BadRequestException('被引用的版本不可删除！');
    }

    // 存在分类数据的版本不能删除
    const category = await this.prisma.machineryDictionaryCategory.findFirst({
      select: {
        id: true
      },
      where: {
        tenantId,
        orgId,
        machineryDictionaryVersionId: id,
        isDeleted: false
      }
    });
    if (category) {
      throw new BadRequestException('该版本下存在数据，不可删除！');
    }
  }

  // 获取项目引用数
  async getProjectRefInfo(
    tenantId: string,
    orgId: string,
    id?: string
  ): Promise<{
    projectRefCount: number;
  }> {
    // 项目引用数统计
    const projectRefCount =
      await this.prisma.accountMachineryDictionaryVersion.count({
        where: {
          tenantId,
          isDeleted: false,
          versionId: id
        }
      });
    return { projectRefCount };
  }

  // 获取所有的版本的项目引用数，按版本分组统计
  async getAllProjectRefInfo(tenantId: string) {
    // 项目引用数统计
    return await this.prisma.accountMachineryDictionaryVersion.groupBy({
      where: {
        tenantId,
        isDeleted: false
      },
      _count: {
        _all: true
      },
      by: ['versionId']
    });
  }
}
