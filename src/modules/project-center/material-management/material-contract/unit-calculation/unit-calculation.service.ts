import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import {
  UnitCalculationCreateDto,
  UnitCalculationQueryDto,
  UnitCalculationUpdateDto
} from './unit-calculation.dto';

@Injectable()
export class UnitCalculationService {
  constructor(private readonly prisma: PrismaService) {}

  async getList(query: UnitCalculationQueryDto, reqUser: IReqUser) {
    const { materialDetailId, materialContractId } = query;
    const { tenantId, orgId } = reqUser;
    const materialDictionaryDetail =
      await this.prisma.materialDictionaryDetail.findUnique({
        where: {
          id: materialDetailId,
          tenantId: tenantId,
          isDeleted: false
        }
      });
    const res = await this.prisma.$queryRaw<any[]>`
      select 
      null as id,
      metering_unit as unit,
      1 as factor,
      null as remark,
      false as is_original,
      false as is_operation,
      null as material_detail_id
      from material_dictionary_detail where id = ${materialDetailId} and tenant_id = ${tenantId} and is_deleted = false

      UNION all

      select id, unit, factor, remark, false as is_original, false as is_operation, null as material_detail_id from material_dictionary_unit_calculation mduc
      where material_dictionary_detail_id = ${materialDetailId}
      and is_deleted = false

      UNION all

      select id, unit, factor, remark, is_original, true as is_operation, material_detail_id from material_contract_unit_calculation
      where material_detail_id = ${materialDetailId}
      and material_contract_id = ${materialContractId}
      and is_deleted = false
      and tenant_id = ${tenantId}
      and org_id = ${orgId}
      order by is_operation asc, is_original desc
    `;
    return {
      dictionaryUnit: materialDictionaryDetail?.meteringUnit ?? '',
      list: res
    };
  }

  /**
   * 初始化单位换算（补充协议）
   * @param txPrisma
   * @param materialContractId
   * @param data
   * @param reqUser
   */
  async init(
    txPrisma: PrismaService,
    materialContractId: string,
    reqUser: IReqUser,
    parentId: string
  ) {
    const { orgId, tenantId, id: userId } = reqUser;
    let data;
    if (parentId) {
      // 查询下是否有补充协议
      const child = await this.prisma.materialContract.findFirst({
        where: {
          parentId,
          isDeleted: false,
          tenantId,
          orgId
        },
        orderBy: {
          createAt: 'desc'
        }
      });
      if (child) {
        parentId = child.id;
      }
      // 有父级查询父级
      data = await this.prisma.materialContractUnitCalculation.findMany({
        select: {
          materialDetailId: true,
          factor: true,
          remark: true,
          unit: true
        },
        where: {
          materialContractId: parentId,
          isDeleted: false,
          tenantId,
          orgId
        },
        orderBy: {
          createAt: 'asc'
        }
      });
      await txPrisma.materialContractUnitCalculation.createMany({
        data: data.map((item) => ({
          ...item,
          isOriginal: true,
          materialContractId,
          tenantId,
          orgId,
          createBy: userId,
          updateBy: userId
        }))
      });
    }
  }

  async add(reqUser: IReqUser, data: UnitCalculationCreateDto) {
    const { tenantId, orgId, id: userId } = reqUser;
    return await this.prisma.materialContractUnitCalculation.create({
      data: {
        ...data,
        tenantId,
        orgId,
        createBy: userId,
        updateBy: userId
      }
    });
  }

  async update(id: string, reqUser: IReqUser, data: UnitCalculationUpdateDto) {
    const { tenantId, orgId, id: userId } = reqUser;
    return await this.prisma.materialContractUnitCalculation.update({
      where: {
        id,
        orgId,
        tenantId,
        isDeleted: false
      },
      data: {
        ...data,
        updateBy: userId
      }
    });
  }

  async delete(id: string, reqUser: IReqUser) {
    return await this.prisma.materialContractUnitCalculation.update({
      where: {
        id
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }
}
