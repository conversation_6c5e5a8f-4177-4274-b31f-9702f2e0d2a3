import { Injectable } from '@nestjs/common';
import * as dayjs from 'dayjs';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { PurchaseType } from '@/prisma/generated/enums';

import {
  InspectionBillListResponseDto,
  InspectionTimeListResponseDto,
  QueryInspectionBillListDto,
  UpdateInspectionBillListDto
} from '../material-incoming-inspection.dto';
import { MaterialIncomingInspectionRepository } from '../repositories/inspection-bill.repositories';

@Injectable()
export class MaterialIncomingInspectionListService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly repository: MaterialIncomingInspectionRepository
  ) {}

  async getTimeList(
    reqUser: IReqUser
  ): Promise<InspectionTimeListResponseDto[]> {
    // 实现获取时间列表的逻辑
    const dates = await this.prisma.materialIncomingInspection.findMany({
      select: {
        year: true,
        month: true,
        day: true
      },
      where: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      },
      orderBy: [{ year: 'desc' }, { month: 'desc' }, { day: 'desc' }]
    });

    const resultMap: Record<string, InspectionTimeListResponseDto> = {};
    for (const time of dates) {
      // 添加父级，年_月
      if (!resultMap[`${time.year}_${time.month}`]) {
        resultMap[`${time.year}_${time.month}`] = {
          id: `${time.year}_${time.month}`,
          parentId: null,
          year: time.year,
          month: time.month,
          count: 0
        };
      }
      resultMap[`${time.year}_${time.month}`].count += 1;

      // 添加子级 年_月_日
      if (!resultMap[`${time.year}_${time.month}_${time.day}`]) {
        resultMap[`${time.year}_${time.month}_${time.day}`] = {
          id: `${time.year}_${time.month}_${time.day}`,
          parentId: `${time.year}_${time.month}`,
          year: time.year,
          month: time.month,
          day: time.day,
          count: 0
        };
      }
      resultMap[`${time.year}_${time.month}_${time.day}`].count += 1;
    }

    return Object.values(resultMap);
  }

  async getBillList(
    reqUser: IReqUser,
    query: QueryInspectionBillListDto
  ): Promise<InspectionBillListResponseDto[]> {
    const inspectionBills: InspectionBillListResponseDto[] =
      await this.repository.selectBillList(reqUser, query);

    return inspectionBills;
  }

  async addInspectionBill(
    reqUser: IReqUser
  ): Promise<InspectionBillListResponseDto> {
    // 获取进场时间
    const dateNow = dayjs();
    const year = dateNow.year();
    const month = dateNow.month() + 1;
    const day = dateNow.date();

    // 生成单据编码
    const code = await this.generateInspectionBillCode(reqUser, year, month);

    const data: InspectionBillListResponseDto =
      await this.prisma.materialIncomingInspection.create({
        data: {
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          creator: reqUser.nickname,
          createBy: reqUser.id,
          updateBy: reqUser.id,
          code,
          purchaseType: PurchaseType.SELF_PURCHASE,
          year,
          month,
          day
        },
        select: {
          materialReceiptStatus: true,
          id: true,
          orgId: true,
          code: true,
          purchaseType: true,
          creator: true,
          year: true,
          month: true,
          day: true,
          submitStatus: true,
          auditStatus: true,
          contractId: true,
          contractName: true,
          supplierId: true,
          supplierName: true
        }
      });

    const orgInfo = await this.prisma.$queryRaw<any[]>`
      select name from platform_meta.org where tenant_id = ${reqUser.tenantId} and id = ${data.orgId}
    `;
    data.orgName = orgInfo[0]?.name || '';
    return data;
  }

  private async generateInspectionBillCode(
    reqUser: IReqUser,
    year: number,
    month: number
  ): Promise<string> {
    const code = ['验', `${year}${String(month).padStart(2, '0')}`, '001'];
    const lastInspectionBill =
      await this.prisma.materialIncomingInspection.findFirst({
        where: {
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        },
        orderBy: {
          code: 'desc'
        }
      });

    if (lastInspectionBill) {
      const lastCode = lastInspectionBill.code;
      const lastCodeNumber = parseInt(lastCode.split('-')[2], 10);
      code[2] = String(lastCodeNumber + 1).padStart(3, '0');
    }

    return code.join('-');
  }

  async editInspectionBill(
    reqUser: IReqUser,
    data: UpdateInspectionBillListDto
  ) {
    await this.prisma.materialIncomingInspection.update({
      where: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false,
        id: data.id
      },
      data: {
        ...data,
        updateBy: reqUser.id,
        updateAt: new Date()
      }
    });
    return true;
  }

  async deleteInspectionBill(reqUser: IReqUser, id: string) {
    await this.prisma.$transaction([
      this.prisma.materialIncomingInspection.update({
        where: {
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false,
          id
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id,
          updateAt: new Date()
        }
      }),
      this.prisma.materialIncomingInspectionDetail.updateMany({
        where: {
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false,
          incomingInspectionId: id
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id,
          updateAt: new Date()
        }
      }),
      this.prisma.materialIncomingInspectionAttachment.updateMany({
        where: {
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false,
          incomingInspectionId: id
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id,
          updateAt: new Date()
        }
      })
    ]);

    return true;
  }
}
