import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import {
  ContractTemplateClassifyType,
  MaterialType,
  Prisma
} from '@/prisma/generated';

import {
  InspectionMaterialDetailListResponseDto,
  MaterialCategoryListResponseDto
} from '../material-incoming-inspection.dto';

@Injectable()
export class MaterialIncomingInspectionDetailRepository {
  constructor(private readonly prisma: PrismaService) {}

  // 获取甲方信息（“建设单位”）
  async selectPartAInfo(reqUser: IReqUser) {
    const partAInfo: Record<string, any>[] = await this.prisma.$queryRaw`
    select
      base_field.id
      ,base_ledger.value
    from basic_project_info_field_detail as base_field
    join basic_project_info_ledger as base_ledger
      on base_ledger.is_deleted = false
      and base_ledger.org_id = ${reqUser.orgId}
      and base_ledger.basic_project_info_field_detail_id = base_field.id
    where base_field.is_deleted = false
      and base_field.name = '建设单位'
  `;

    return partAInfo[0];
  }

  // 获取最新的(合同/补充协议)
  async selectLastContractInfo(
    reqUser: IReqUser,
    inspectionBillId: string
  ): Promise<Record<string, string>> {
    const contractInfo: Record<string, string>[] = await this.prisma.$queryRaw`
      select
        coalesce((
          select max(child.id)
          from material_contract child
          where child.is_deleted = false
            and child.tenant_id = parent.tenant_id
            and child.org_id = parent.org_id
            and child.parent_id = parent.id
        ), parent.id) as id
      from material_contract parent
      where parent.is_deleted = false
        and parent.tenant_id = ${reqUser.tenantId}
        and parent.org_id = ${reqUser.orgId}
        and parent.id = (
          select contract_id
          from material_incoming_inspection
          where is_deleted = false
            and tenant_id = ${reqUser.tenantId}
            and org_id = ${reqUser.orgId}
            and id = ${inspectionBillId}
        )
    `;
    return contractInfo[0];
  }

  // 根据合同，获取合同下选择的材料类别信息（树级）
  async selectContractMaterialCategories(
    reqUser: IReqUser,
    contractId: string
  ): Promise<MaterialCategoryListResponseDto[]> {
    const result: MaterialCategoryListResponseDto[] = await this.prisma
      .$queryRaw`
      with temp_consumer_material as (
        select
          material_dictionary_category_id
        from contract_consume_material_details
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and org_id = ${reqUser.orgId}
          and material_contract_id = ${contractId}
      )
      , temp_contract_concrete_material as (
        select
          material_dictionary_category_id
        from contract_concrete_details
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and org_id = ${reqUser.orgId}
          and material_contract_id = ${contractId}
      )
      ,temp_turnover_material as (
        select
          material_dictionary_category_id
        from contract_turnover_material_details
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and org_id = ${reqUser.orgId}
          and material_contract_id = ${contractId}
      )
      , leaf_material_category as (
        select distinct
          material_dictionary_version_id
          ,full_id
        from material_dictionary_category
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and org_id = ${reqUser.orgId}
          and id in (
            select material_dictionary_category_id from(
              select material_dictionary_category_id from temp_consumer_material
              union all
              select material_dictionary_category_id from temp_contract_concrete_material
              union all
              select material_dictionary_category_id from temp_turnover_material 
            ) tt
          )
      )
      select distinct
        parent.id
        ,parent.parent_id
        ,parent.name
        ,parent.code
        ,parent.type
        ,parent.level
        ,parent.remark
      from material_dictionary_category parent
      join leaf_material_category child
        on child.material_dictionary_version_id = parent.material_dictionary_version_id
        and POSITION(parent.id in child.full_id) > 0
      where parent.is_deleted = false
        and parent.tenant_id = ${reqUser.tenantId}
        and parent.org_id = ${reqUser.orgId}
    `;

    return result;
  }

  // 查询项目成本核算设置的全部材料分类
  async selectAllMaterialCategories(
    reqUser: IReqUser,
    materialType: MaterialType | null
  ): Promise<MaterialCategoryListResponseDto[]> {
    const result: MaterialCategoryListResponseDto[] = await this.prisma
      .$queryRaw`
      select
        mdc.id
        ,mdc.parent_id
        ,mdc.name
        ,mdc.code
        ,mdc.type
        ,mdc.level
        ,mdc.remark
      from account_material_dictionary_category amdc
      join material_dictionary_category mdc
        on mdc.is_deleted = false
        and mdc.is_active = true
        and mdc.id = amdc.category_id
        and mdc.tenant_id = amdc.tenant_id
        ${materialType ? Prisma.sql`and mdc.type = ${materialType}::"MaterialType"` : ''}
      where amdc.is_deleted = false
        and amdc.tenant_id = ${reqUser.tenantId}
        and amdc.org_id = ${reqUser.orgId}
    `;

    return result;
  }

  // 获取合同下指定材料分类的材料明细
  async selectContractMaterials(
    reqUser: IReqUser,
    materialCategoryId: string | null,
    contractId: string
  ): Promise<InspectionMaterialDetailListResponseDto[]> {
    const result: InspectionMaterialDetailListResponseDto[] = await this.prisma
      .$queryRaw`
      with temp_consumer_material as (
        select
          material_dictionary_detail_id
          ,unit
        from contract_consume_material_details
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and org_id = ${reqUser.orgId}
          and material_contract_id = ${contractId}
      )
      , temp_contract_concrete_material as (
        select
          material_dictionary_detail_id
          ,unit
        from contract_concrete_details
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and org_id = ${reqUser.orgId}
          and material_contract_id = ${contractId}
      )
      ,temp_turnover_material as (
        select
          material_dictionary_detail_id
          ,unit
        from contract_turnover_material_details
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and org_id = ${reqUser.orgId}
          and material_contract_id = ${contractId}
      )
      ,temp_contract_material as (
        select material_dictionary_detail_id, unit from temp_consumer_material
        union all
        select material_dictionary_detail_id, unit from temp_contract_concrete_material
        union all
        select material_dictionary_detail_id, unit from temp_turnover_material
      )
      ,temp_material_category as (
        select distinct id
        from material_dictionary_category
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and position(${materialCategoryId} in full_id) > 0
      )
      select distinct
        m.id
        ,m.name
        ,m.code
        ,m.specification_model as spec
        ,tcm.unit
        ,m.remark
      from material_dictionary_detail m
      join temp_contract_material tcm
        on tcm.material_dictionary_detail_id = m.id
      where m.is_deleted = false
        and m.tenantId = ${reqUser.tenantId}
        ${
          materialCategoryId
            ? Prisma.sql`and m.material_dictionary_category_id in (select id from temp_material_category)`
            : Prisma.empty
        }
      order by m.material_dictionary_category_id, m.sort
    `;

    return result;
  }

  // 获取材料分类的所有材料明细
  async selectAllMaterials(
    reqUser: IReqUser,
    materialCategoryId: string | null
  ): Promise<InspectionMaterialDetailListResponseDto[]> {
    const result: InspectionMaterialDetailListResponseDto[] = await this.prisma
      .$queryRaw`
      with temp_material_category as (
        select distinct id
        from material_dictionary_category
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and position(${materialCategoryId} in full_id) > 0
      )
      select
        md.id
        ,md.code
        ,md.name
        ,md.specification_model as spec
        ,md.metering_unit as unit
        ,md.remark
      from material_dictionary_detail md
      where md.is_deleted = false
        and md.tenant_id = ${reqUser.tenantId}
        ${
          materialCategoryId
            ? Prisma.sql`and md.material_dictionary_category_id in (select id from temp_material_category)`
            : Prisma.empty
        }
      order by md.material_dictionary_category_id, md.sort
    `;

    return result;
  }

  // 获取验收单明细可选择的单位下拉列表
  async selectInspectionDetailUnits(
    tenantId: string,
    orgId: string,
    contractId: string,
    materialIds: string[]
  ) {
    const result: any[] = await this.prisma.$queryRaw`
      with material_unit as (
        select
          id as material_id
          ,metering_unit as unit
        from material_dictionary_detail
        where is_deleted = false
          and id in (${Prisma.join(materialIds)})
      )
      , material_contract_unit as (
        select
          material_detail_id as material_id
          ,unit
        from material_contract_unit_calculation
        where is_deleted = false
          and tenant_id = ${tenantId}
          and org_id = ${orgId}
          and material_contract_id = ${contractId}
          and material_detail_id in (${Prisma.join(materialIds)})
      )
      select
        tt.material_id
        ,string_agg(tt.unit, ',') optional_units
      from (
        select * from material_unit
        union all
        select * from material_contract_unit
      ) tt
      group by tt.material_id
    `;

    return result;
  }

  // 获取合同编制的合同列表(不含补充协议)
  async selectContracts(
    tenantId: string,
    orgId: string,
    contractTemplateClassifyType: ContractTemplateClassifyType
  ) {
    const result: any[] = await this.prisma.$queryRaw`
      select
        mc.id
        ,mc.name
        ,mc.party_b
        ,mc.party_b_type
      from material_contract as mc
      join contract_template as ct
        on ct.is_deleted = false
        and ct.tenant_id = mc.tenant_id
        and ct.id = mc.contract_template_id
        and ct.classify = ${contractTemplateClassifyType}::"ContractTemplateClassifyType"
      where mc.is_deleted = false
        and mc.tenant_id = ${tenantId}
        and mc.org_id = ${orgId}
        and mc.parent_id is null
    `;

    return result;
  }

  async updateInspectionDetailOrderNo(
    reqUser: IReqUser,
    fromId: string,
    toId: string
  ) {
    await this.prisma.$executeRaw`
      WITH target_records AS (
        SELECT
          id,
          order_no,
          CASE
            WHEN id = ${fromId} THEN ${toId}
            WHEN id = ${toId} THEN ${fromId}
          END AS swap_with_id
        FROM material_incoming_inspection_detail
        WHERE id IN (${fromId}, ${toId})
          AND is_deleted = false
          AND tenant_id = ${reqUser.tenantId}
          AND org_id = ${reqUser.orgId}
      ),
      order_mapping AS (
        SELECT
          t1.id,
          t2.order_no AS new_order_no
        FROM target_records t1
        JOIN target_records t2 ON t1.swap_with_id = t2.id
      )
      UPDATE material_incoming_inspection_detail
      SET order_no = order_mapping.new_order_no
      FROM order_mapping
      WHERE material_incoming_inspection_detail.id = order_mapping.id
    `;

    return true;
  }
}
