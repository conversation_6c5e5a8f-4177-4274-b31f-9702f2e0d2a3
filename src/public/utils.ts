import {
  ChangeRecordDetailDto,
  DetailsFieldType
} from '@/modules/enterprise-center/enterprise-standard/supplier-directory/supplier-directory-change-record/supplier-directory-change-record.dto';

export class Utils {
  /**
   * 构建树结构
   * @param list
   * @param parentId
   * @returns
   */

  static buildTree(list: any[], parentId: number | null) {
    const result: any[] = [];

    for (const item of list) {
      if (item.parentId === parentId) {
        const children = this.buildTree(list, item.id);

        if (children.length) {
          item.children = children;
        }

        result.push(item);
      }
    }

    return result;
  }

  // 数据对比
  static detectChanges(original: any, updated: any): ChangeRecordDetailDto[] {
    const changes = [];
    const fields = Object.keys(original);

    for (const field of fields) {
      if (field === 'supplierPubDirectoryAccessory') {
        continue;
      }
      const oldVal = this.formatValue(field, original[field]);
      const newVal = this.formatValue(field, updated[field]);
      if (typeof oldVal === 'number' && typeof newVal === 'number') {
        if (parseFloat(oldVal) !== parseFloat(newVal)) {
          changes.push({
            fieldName: field,
            oldValue: oldVal,
            newValue: newVal,
            fieldType: DetailsFieldType.GENERAL
          });
        }
      } else {
        if (oldVal !== newVal) {
          changes.push({
            fieldName: field,
            oldValue: oldVal,
            newValue: newVal,
            fieldType: DetailsFieldType.GENERAL
          });
        }
      }
    }

    return changes;
  }

  static formatValue(field: string, value: any): string {
    const classify = {
      SERVICE_OUTSOURCING: '服务外包',
      MECHANICAL_LEASING: '机械租赁',
      LABOR_SUBCONTRACTING: '劳务分包',
      MATERIAL_PURCHASING: '物资采购',
      PROFESSIONAL_SUBCONTRACTING: '专业分包'
    };
    const taxpayerQualification = {
      GENERAL: '一般纳税人',
      SMALL_SCALE: '小规模纳税人'
    };
    if (value instanceof Date) return value.toISOString();
    if (value === null) return '空值';
    if (value === undefined) return '未定义';
    // if (typeof value === 'object') return JSON.stringify(value);
    if (field === 'classify') {
      // 供应商分类单独处理
      return value
        .map((item: any) => {
          const key = item as keyof typeof classify;
          return classify[key];
        })
        .join(',');
    }
    if (field === 'taxpayerQualification') {
      const key = value as keyof typeof taxpayerQualification;
      return taxpayerQualification[key];
    }
    return String(value);
  }

  /**
   * 自动转换文件大小单位（字节 → KB/MB/GB/TB）
   * @param bytes 文件大小（字节）
   * @param options 格式化选项
   * @returns 格式化后的文件大小字符串（如 "2.45 MB"）
   */
  static formatFileSize(
    bytes: number,
    options?: {
      decimalPlaces?: number; // 小数位数，默认 2
      spaceBeforeUnit?: boolean; // 单位前是否添加空格，默认 true
      unit?: 'auto' | 'B' | 'KB' | 'MB' | 'GB' | 'TB'; // 强制使用某个单位，默认 'auto'
    }
  ): string {
    // 处理非正数或无效值
    if (bytes <= 0) return '0 B';

    // 配置默认值
    const {
      decimalPlaces = 2,
      spaceBeforeUnit = true,
      unit = 'auto'
    } = options || {};

    // 单位列表及对应的字节数
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    const space = spaceBeforeUnit ? ' ' : '';

    // 如果指定了单位，则直接转换
    if (unit !== 'auto') {
      const unitIndex = units.indexOf(unit);
      if (unitIndex === -1) throw new Error(`Invalid unit: ${unit}`);

      const value = bytes / Math.pow(1024, unitIndex);
      return `${value.toFixed(decimalPlaces)}${space}${unit}`;
    }

    // 自动选择合适的单位
    let unitIndex = 0;
    let size = bytes;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(decimalPlaces)}${space}${units[unitIndex]}`;
  }
  //  深查判断引用
  // deepSearch(PrismaClient, parentId) {

  // }
}
